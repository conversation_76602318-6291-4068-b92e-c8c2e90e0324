#!/usr/bin/env python3
"""
Test script for RS rating filter functionality in enhanced_analysis.py

This script tests the new RS rating filter feature by creating a sample DataFrame
and testing the groupAndFilter function with different RS rating thresholds.
"""

import sys
import os
sys.path.append('src')

import pandas as pd
from fidelity.chart_analysis.enhanced_analysis import groupAndFilter

def create_test_dataframe():
    """Create a sample DataFrame for testing"""
    data = {
        'Symbol': ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA'],
        'Sector': ['Technology', 'Technology', 'Technology', 'Consumer Discretionary', 'Technology'],
        'Industry': ['Consumer Electronics', 'Software', 'Internet Services', 'Auto Manufacturers', 'Semiconductors'],
        'Security Type': ['Common Stock', 'Common Stock', 'Common Stock', 'Common Stock', 'Common Stock'],
        'Security Price': [150.0, 300.0, 2500.0, 200.0, 500.0],
        'Volume (10 Day Avg)': [50.0, 30.0, 25.0, 40.0, 35.0],
        '% Above 52 Week Low': [60.0, 70.0, 55.0, 80.0, 65.0],
        'Earnings Announcements (Upcoming)': [10, 15, 8, 12, 20],
        'EPS Growth (TTM vs Prior TTM)': [20.0, 25.0, 15.0, 30.0, 40.0],
        'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': [18.0, 22.0, 12.0, 35.0, 45.0],
        'Revenue Growth (TTM vs. Prior TTM)': [15.0, 20.0, 10.0, 25.0, 30.0],
        'Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)': [12.0, 18.0, 8.0, 28.0, 32.0],
        'EPS Growth (Proj Next Yr vs. This Yr)': [25.0, 30.0, 20.0, 60.0, 55.0]
    }
    
    return pd.DataFrame(data)

def test_rs_filter():
    """Test the RS rating filter functionality"""
    print("Testing RS Rating Filter Functionality")
    print("=" * 50)
    
    # Create test DataFrame
    df = create_test_dataframe()
    print(f"Original DataFrame has {len(df)} stocks:")
    print(df[['Symbol', 'Sector']].to_string(index=False))
    print()
    
    # Test without RS filter
    print("Testing without RS filter...")
    result_no_filter = groupAndFilter(df, filter='N', rs_rating_filter=None)
    print(f"Result without RS filter: {len(result_no_filter)} stocks")
    print()
    
    # Test with RS filter = 70
    print("Testing with RS filter = 70...")
    try:
        result_with_filter = groupAndFilter(df, filter='N', rs_rating_filter=70)
        print(f"Result with RS filter >= 70: {len(result_with_filter)} stocks")
        if len(result_with_filter) > 0:
            print("Stocks that passed RS filter:")
            print(result_with_filter[['Symbol', 'RS_Rating']].to_string(index=False))
    except Exception as e:
        print(f"Error during RS filtering: {e}")
    print()
    
    print("Test completed!")

if __name__ == "__main__":
    test_rs_filter()
